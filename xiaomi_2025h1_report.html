<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小米汽车 2025年上半年报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            font-size: 3em;
            color: #FF6900;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .subtitle {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: 700;
            color: #FF6900;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1em;
            color: #666;
        }

        .chart-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .chart-container {
            width: 100%;
            height: 400px;
            position: relative;
        }

        .products-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .product-card {
            background: #fff;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-10px);
        }

        .product-image {
            width: 100%;
            height: 250px;
            object-fit: cover;
        }

        .product-info {
            padding: 25px;
        }

        .product-name {
            font-size: 1.5em;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }

        .product-specs {
            list-style: none;
            margin-bottom: 20px;
        }

        .product-specs li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }

        .product-price {
            font-size: 1.3em;
            font-weight: 700;
            color: #FF6900;
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        h2 {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .section-title {
            font-size: 2em;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
        }

        .sales-chart {
            width: 100%;
            height: 300px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            position: relative;
        }

        .chart-bars {
            display: flex;
            align-items: end;
            justify-content: space-around;
            height: 200px;
            margin-top: 20px;
        }

        .bar {
            width: 60px;
            background: linear-gradient(135deg, #FF6900, #FF8C42);
            border-radius: 5px 5px 0 0;
            position: relative;
            transition: all 0.3s ease;
        }

        .bar:hover {
            opacity: 0.8;
        }

        .bar-label {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.9em;
            color: #666;
        }

        .bar-value {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8em;
            color: #FF6900;
            font-weight: 600;
        }

        .footer {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            margin-top: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .highlight-box {
            background: linear-gradient(135deg, #FF6900, #FF8C42);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
        }

        .highlight-box h3 {
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            h1 {
                font-size: 2em;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>小米汽车 2025年上半年报告</h1>
            <div class="subtitle">Xiaomi Auto H1 2025 Report</div>
            <div class="highlight-box">
                <h3>销售总览</h3>
                <p>总销量：157,000台 | 同比增长：25.5% | 市场份额：18.3%</p>
            </div>
        </header>

        <section class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">157,000</div>
                <div class="stat-label">总销量 (台)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">25.5%</div>
                <div class="stat-label">同比增长</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">18.3%</div>
                <div class="stat-label">市场份额</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">3</div>
                <div class="stat-label">车型数量</div>
            </div>
        </section>

        <section class="chart-section">
            <h2 class="section-title">2025年上半年月度销量</h2>
            <div class="sales-chart">
                <div class="chart-bars">
                    <div class="bar" style="height: 125px;">
                        <span class="bar-value">25,000</span>
                        <span class="bar-label">1月</span>
                    </div>
                    <div class="bar" style="height: 115px;">
                        <span class="bar-value">23,000</span>
                        <span class="bar-label">2月</span>
                    </div>
                    <div class="bar" style="height: 135px;">
                        <span class="bar-value">27,000</span>
                        <span class="bar-label">3月</span>
                    </div>
                    <div class="bar" style="height: 130px;">
                        <span class="bar-value">26,000</span>
                        <span class="bar-label">4月</span>
                    </div>
                    <div class="bar" style="height: 140px;">
                        <span class="bar-value">28,000</span>
                        <span class="bar-label">5月</span>
                    </div>
                    <div class="bar" style="height: 140px;">
                        <span class="bar-value">28,000</span>
                        <span class="bar-label">6月</span>
                    </div>
                </div>
            </div>
        </section>

        <section class="products-section">
            <h2 class="section-title">车型展示</h2>
            <div class="products-grid">
                <div class="product-card">
                    <img src="assets/img/SU7.jpg" alt="小米SU7" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">小米 SU7</h3>
                        <ul class="product-specs">
                            <li><span>销量:</span><span>110,000台</span></li>
                            <li><span>售价:</span><span>25.9-32.5万</span></li>
                            <li><span>交付时间:</span><span>2-4周</span></li>
                            <li><span>续航里程:</span><span>668-800km</span></li>
                            <li><span>百公里加速:</span><span>2.78秒</span></li>
                        </ul>
                        <div class="product-price">旗舰电动轿车</div>
                    </div>
                </div>

                <div class="product-card">
                    <img src="assets/img/SU7.jpg" alt="小米SU7 Ultra" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">小米 SU7 Ultra</h3>
                        <ul class="product-specs">
                            <li><span>销量:</span><span>35,000台</span></li>
                            <li><span>售价:</span><span>31.9-35.5万</span></li>
                            <li><span>交付时间:</span><span>4-6周</span></li>
                            <li><span>续航里程:</span><span>630-730km</span></li>
                            <li><span>百公里加速:</span><span>2.78秒</span></li>
                        </ul>
                        <div class="product-price">高性能版本</div>
                    </div>
                </div>

                <div class="product-card">
                    <img src="assets/img/SU7.jpg" alt="小米YU7" class="product-image">
                    <div class="product-info">
                        <h3 class="product-name">小米 YU7</h3>
                        <ul class="product-specs">
                            <li><span>销量:</span><span>12,000台</span></li>
                            <li><span>售价:</span><span>30.5-36.8万</span></li>
                            <li><span>交付时间:</span><span>6-8周</span></li>
                            <li><span>续航里程:</span><span>700-850km</span></li>
                            <li><span>百公里加速:</span><span>3.2秒</span></li>
                        </ul>
                        <div class="product-price">SUV车型</div>
                    </div>
                </div>
            </div>
        </section>

        <div class="highlight-box">
            <h3>市场表现亮点</h3>
            <p>• 小米SU7成为最畅销电动轿车车型之一</p>
            <p>• 高性能版本SU7 Ultra需求强劲</p>
            <p>• YU7 SUV车型持续增长，预计下半年将有更大突破</p>
        </div>

        <footer class="footer">
            <h3>小米汽车 2025年上半年总结</h3>
            <p>报告生成时间：2025年8月12日</p>
            <p>数据来源：小米汽车官方统计数据</p>
            <p>© 2025 小米汽车 - 智能出行新时代</p>
        </footer>
    </div>

    <script>
        // 添加动画效果
        window.addEventListener('load', function() {
            const bars = document.querySelectorAll('.bar');
            bars.forEach((bar, index) => {
                setTimeout(() => {
                    bar.style.transform = 'scaleY(1)';
                    bar.style.transformOrigin = 'bottom';
                }, index * 200);
            });

            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 150);
            });
        });

        // 初始化动画样式
        const style = document.createElement('style');
        style.textContent = `
            .bar {
                transform: scaleY(0);
                transition: transform 0.8s ease;
            }
            .stat-card {
                opacity: 0;
                transform: translateY(20px);
                transition: all 0.6s ease;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>